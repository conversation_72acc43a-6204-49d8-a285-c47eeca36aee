# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\File\\FileFlutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\OneDrive - HUFI\\Chuong5\\bt1" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\File\\FileFlutter\\flutter"
  "PROJECT_DIR=D:\\OneDrive - HUFI\\Chuong5\\bt1"
  "FLUTTER_ROOT=D:\\File\\FileFlutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\OneDrive - HUFI\\Chuong5\\bt1\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\OneDrive - HUFI\\Chuong5\\bt1"
  "FLUTTER_TARGET=D:\\OneDrive - HUFI\\Chuong5\\bt1\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\OneDrive - HUFI\\Chuong5\\bt1\\.dart_tool\\package_config.json"
)
